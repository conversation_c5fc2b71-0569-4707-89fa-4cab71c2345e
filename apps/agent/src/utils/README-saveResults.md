# Creator Scouting Results Save Utility

This utility provides comprehensive functionality to save creator scouting workflow results to local JSON files with rich metadata and multiple output formats.

## Features

- 🗂️ **Multiple Output Formats**: Detailed, summary, and CSV-ready formats
- 📊 **Rich Metadata**: Includes timestamps, input parameters, and filtering statistics
- 🏷️ **Creator Metrics**: Complete creator profiles with standard and advanced metrics
- 📁 **Flexible Storage**: Custom directories and filenames
- 🔄 **Automatic Timestamping**: Prevents file overwrites with timestamp-based naming

## Quick Start

```typescript
import { saveCreatorResultsToFile } from '@/utils/saveResults';

// Save workflow results to file
const filepath = saveCreatorResultsToFile(workflowResults, inputParameters);
console.log(`Results saved to: ${filepath}`);
```

## API Reference

### `saveCreatorResultsToFile(results, inputData, customDir?, customFilename?)`

Saves creator scouting results with full metadata and creator metrics.

**Parameters:**
- `results`: Workflow output containing creators and filtering statistics
- `inputData`: Original workflow input parameters
- `customDir` (optional): Custom directory name (default: 'scouting-results')
- `customFilename` (optional): Custom filename prefix (default: 'creator-scout-results')

**Returns:** Full path to the saved file

### `saveCreatorResultsWithFormat(results, inputData, format)`

Saves results in specific formats optimized for different use cases.

**Formats:**
- `'detailed'`: Complete results with all metadata and metrics (default)
- `'summary'`: Essential information and top 10 creators only
- `'csv-ready'`: Flattened data structure ready for CSV export

### `ensureResultsDirectory(customDir?)`

Creates the results directory if it doesn't exist.

## Output File Structure

### Detailed Format
```json
{
  "metadata": {
    "timestamp": "2024-01-15T10:30:00.000Z",
    "inputParameters": {
      "targetCreatorDescription": "...",
      "filterMode": "STRICT",
      "desiredCreatorCount": 30
    },
    "summary": {
      "totalScoutedCreators": 150,
      "totalQualifiedCreators": 25,
      "targetCreatorCount": 30,
      "filterMode": "STRICT"
    },
    "filterSummary": {
      "mode": "STRICT",
      "total_analyzed": 150,
      "total_qualified": 25,
      "avg_match_score": 0.82,
      "tier_breakdown": {
        "PERFECT": 8,
        "EXCELLENT": 12,
        "GOOD": 4,
        "ACCEPTABLE": 1
      }
    }
  },
  "creators": [
    {
      "url": "https://www.tiktok.com/@creator_handle",
      "reason": "Perfect match for gaming content...",
      "match_score": 0.98,
      "tier": "PERFECT",
      "creatorMetrics": {
        "unique_id": "creator_handle",
        "nickname": "Creator Name",
        "follower_count": 1250000,
        "aweme_count": 89,
        "create_time": 1609459200,
        "region": "US",
        "language": "en",
        "recentVideosCollected": 10,
        "medianViews": 456789,
        "medianLikes": 12345,
        "medianComments": 567,
        "medianShares": 234,
        "avgEngagementRate": 3.45,
        "totalViews": 5000000,
        "totalLikes": 150000,
        "totalComments": 8900,
        "totalShares": 3400
      }
    }
  ]
}
```

## Creator Metrics Included

### Standard Metrics
- `unique_id`: TikTok handle
- `nickname`: Display name
- `follower_count`: Number of followers
- `aweme_count`: Number of posts/videos
- `create_time`: Account creation timestamp
- `region`: Creator's region/country
- `language`: Primary language

### Advanced Video Metrics
- `recentVideosCollected`: Number of recent videos analyzed
- `medianViews`: Median view count across recent videos
- `medianLikes`: Median like count
- `medianComments`: Median comment count
- `medianShares`: Median share count
- `avgEngagementRate`: Average engagement rate percentage
- `totalViews`, `totalLikes`, `totalComments`, `totalShares`: Aggregate totals

## Usage Examples

### Basic Usage
```typescript
import { saveCreatorResultsToFile } from '@/utils/saveResults';

// After running the workflow
const result = await workflow.run(inputData);
const savedPath = saveCreatorResultsToFile(result, inputData);
```

### Multiple Formats
```typescript
import { saveCreatorResultsWithFormat } from '@/utils/saveResults';

// Save detailed results
const detailedPath = saveCreatorResultsWithFormat(result, inputData, 'detailed');

// Save summary for quick review
const summaryPath = saveCreatorResultsWithFormat(result, inputData, 'summary');

// Save CSV-ready data for spreadsheet analysis
const csvPath = saveCreatorResultsWithFormat(result, inputData, 'csv-ready');
```

### Custom Directory and Filename
```typescript
// Save to custom location with custom name
const customPath = saveCreatorResultsToFile(
  result,
  inputData,
  'my-analysis-results',
  'gaming-creators-jan-2024'
);
```

## File Naming Convention

Files are automatically named with timestamps to prevent overwrites:
- `creator-scout-results-2024-01-15T10-30-00-000Z.json`
- `summary-2024-01-15T10-30-00-000Z.json`
- `csv-ready-2024-01-15T10-30-00-000Z.json`

## Integration with Workflow

The workflow supports an optional `saveResultsToFile` parameter:

```typescript
const result = await workflow.run({
  targetCreatorDescription: "...",
  desiredCreatorCount: 30,
  filterMode: 'STRICT',
  saveResultsToFile: true  // Automatically save results
});
```

## Error Handling

All save functions include comprehensive error handling and will log detailed error messages if saving fails. The workflow will continue to run even if saving fails.

```typescript
try {
  const savedPath = saveCreatorResultsToFile(result, inputData);
  console.log(`✅ Results saved: ${savedPath}`);
} catch (error) {
  console.error('❌ Failed to save results:', error);
  // Workflow continues normally
}
```
