/**
 * Example script demonstrating how to save creator scouting results to local JSON files
 * 
 * This example shows:
 * 1. How to use the saveCreatorResultsToFile utility function
 * 2. Different output formats (detailed, summary, csv-ready)
 * 3. Custom directory and filename options
 */

import { saveCreatorResultsToFile, saveCreatorResultsWithFormat, ensureResultsDirectory } from '@/utils/saveResults';

// Example workflow results (this would normally come from running the actual workflow)
const exampleResults = {
  scoutedCreators: 150,
  qualifiedCreators: 25,
  results: [
    {
      url: 'https://www.tiktok.com/@gamer_pro_123',
      reason: 'Perfect match for gaming content with high engagement and English commentary',
      match_score: 0.98,
      tier: 'PERFECT' as const,
      creatorMetrics: {
        unique_id: 'gamer_pro_123',
        nickname: 'Gaming Pro',
        follower_count: 1250000,
        aweme_count: 89,
        create_time: 1609459200, // Jan 1, 2021
        region: 'US',
        language: 'en',
        recentVideosCollected: 10,
        medianViews: 456789,
        medianLikes: 12345,
        medianComments: 567,
        medianShares: 234,
        avgEngagementRate: 3.45,
        totalViews: 5000000,
        totalLikes: 150000,
        totalComments: 8900,
        totalShares: 3400,
      },
    },
    {
      url: 'https://www.tiktok.com/@game_streamer_xyz',
      reason: 'Excellent gaming content creator with diverse game portfolio and strong engagement',
      match_score: 0.89,
      tier: 'EXCELLENT' as const,
      creatorMetrics: {
        unique_id: 'game_streamer_xyz',
        nickname: 'Game Streamer XYZ',
        follower_count: 890000,
        aweme_count: 156,
        create_time: 1577836800, // Jan 1, 2020
        region: 'US',
        language: 'en',
        recentVideosCollected: 10,
        medianViews: 234567,
        medianLikes: 8901,
        medianComments: 345,
        medianShares: 123,
        avgEngagementRate: 4.12,
        totalViews: 3200000,
        totalLikes: 98000,
        totalComments: 5600,
        totalShares: 2100,
      },
    },
    // Add more example creators as needed...
  ],
  filterSummary: {
    mode: 'STRICT' as const,
    total_analyzed: 150,
    total_qualified: 25,
    avg_match_score: 0.82,
    tier_breakdown: {
      PERFECT: 8,
      EXCELLENT: 12,
      GOOD: 4,
      ACCEPTABLE: 1,
    },
  },
};

// Example input parameters
const exampleInputData = {
  targetCreatorDescription: 'US gaming content creators with diverse game portfolio, English commentary, and high engagement',
  useIntelligentChallengeSelection: true,
  desiredCreatorCount: 30,
  filterMode: 'STRICT' as const,
  saveResultsToFile: false,
};

/**
 * Demonstrate basic file saving functionality
 */
async function demonstrateBasicSaving() {
  console.log('🔹 Basic Saving Example');
  console.log('========================');

  try {
    // Save with default settings
    const filepath = saveCreatorResultsToFile(exampleResults, exampleInputData);
    console.log(`✅ Basic save completed: ${filepath}\n`);
  } catch (error) {
    console.error('❌ Basic save failed:', error);
  }
}

/**
 * Demonstrate saving with different formats
 */
async function demonstrateFormatSaving() {
  console.log('🔹 Format Saving Examples');
  console.log('==========================');

  try {
    // Save detailed format (default)
    const detailedPath = saveCreatorResultsWithFormat(exampleResults, exampleInputData, 'detailed');
    console.log(`✅ Detailed format saved: ${detailedPath}`);

    // Save summary format
    const summaryPath = saveCreatorResultsWithFormat(exampleResults, exampleInputData, 'summary');
    console.log(`✅ Summary format saved: ${summaryPath}`);

    // Save CSV-ready format
    const csvPath = saveCreatorResultsWithFormat(exampleResults, exampleInputData, 'csv-ready');
    console.log(`✅ CSV-ready format saved: ${csvPath}\n`);
  } catch (error) {
    console.error('❌ Format saving failed:', error);
  }
}

/**
 * Demonstrate custom directory and filename options
 */
async function demonstrateCustomOptions() {
  console.log('🔹 Custom Options Example');
  console.log('==========================');

  try {
    // Ensure custom directory exists
    const customDir = ensureResultsDirectory('custom-results');
    console.log(`📁 Custom directory ready: ${customDir}`);

    // Save with custom directory and filename
    const customPath = saveCreatorResultsToFile(
      exampleResults,
      exampleInputData,
      'custom-results',
      'gaming-creators-analysis'
    );
    console.log(`✅ Custom save completed: ${customPath}\n`);
  } catch (error) {
    console.error('❌ Custom save failed:', error);
  }
}

/**
 * Demonstrate what the saved file structure looks like
 */
function demonstrateFileStructure() {
  console.log('🔹 File Structure Example');
  console.log('==========================');
  
  const exampleFileStructure = {
    metadata: {
      timestamp: '2024-01-15T10:30:00.000Z',
      inputParameters: exampleInputData,
      summary: {
        totalScoutedCreators: 150,
        totalQualifiedCreators: 25,
        targetCreatorCount: 30,
        filterMode: 'STRICT',
        useIntelligentSelection: true,
      },
      filterSummary: exampleResults.filterSummary,
    },
    creators: exampleResults.results,
  };

  console.log('📄 Saved file structure:');
  console.log(JSON.stringify(exampleFileStructure, null, 2));
  console.log('\n');
}

/**
 * Main demonstration function
 */
async function runSaveResultsDemo() {
  console.log('🚀 Creator Scouting Results Save Demo');
  console.log('=====================================\n');

  // Run all demonstrations
  await demonstrateBasicSaving();
  await demonstrateFormatSaving();
  await demonstrateCustomOptions();
  demonstrateFileStructure();

  console.log('✨ Demo completed! Check the generated files in:');
  console.log('   - ./scouting-results/ (default location)');
  console.log('   - ./custom-results/ (custom location)');
  console.log('\n💡 Tips:');
  console.log('   - Use "detailed" format for complete analysis');
  console.log('   - Use "summary" format for quick overviews');
  console.log('   - Use "csv-ready" format for spreadsheet import');
  console.log('   - Files include timestamps to avoid overwrites');
}

// Run the demo if this file is executed directly
if (require.main === module) {
  runSaveResultsDemo().catch(console.error);
}

export { runSaveResultsDemo };
